//
//  ContentView.swift
//  cdss-demo
//
//  Created by kit.yeung on 2025/4/21.
//

import SwiftUI

struct ContentView: View {
    @StateObject private var watermarkManager = WatermarkManager()
    @StateObject private var screenshotInterceptor = ScreenshotInterceptor()
    @State private var watermarkText = "© 2024 CDSS Demo"
    @State private var showCustomization = false

    var body: some View {
        NavigationView {
            VStack(spacing: 30) {
                // 标题区域
                VStack(spacing: 10) {
                    Image(systemName: "camera.viewfinder")
                        .font(.system(size: 60))
                        .foregroundColor(.blue)

                    Text("截图水印工具")
                        .font(.title)
                        .fontWeight(.bold)

                    Text("为您的截图添加专业水印")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }

                // 功能演示区域
                VStack(spacing: 20) {
                    DemoContentView()
                }
                .padding()
                .background(Color(.systemGray6))
                .cornerRadius(12)

                // 截屏拦截开关
                VStack(alignment: .leading, spacing: 8) {
                    HStack {
                        Text("截屏保护模式")
                            .font(.headline)
                        Spacer()
                        Toggle("", isOn: $screenshotInterceptor.isInterceptionEnabled)
                            .onChange(of: screenshotInterceptor.isInterceptionEnabled) { enabled in
                                if enabled {
                                    screenshotInterceptor.enableScreenshotInterception()
                                } else {
                                    screenshotInterceptor.disableScreenshotInterception()
                                }
                            }
                    }

                    Text(screenshotInterceptor.isInterceptionEnabled ?
                         "🔒 系统截屏将被自动替换为安全内容" :
                         "🔓 系统截屏功能正常")
                        .font(.caption)
                        .foregroundColor(screenshotInterceptor.isInterceptionEnabled ? .green : .secondary)
                }
                .padding()
                .background(Color(.systemGray6))
                .cornerRadius(8)

                // 水印文字输入
                VStack(alignment: .leading, spacing: 8) {
                    Text("水印文字")
                        .font(.headline)

                    TextField("输入水印文字", text: $watermarkText)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                }

                // 操作按钮
                VStack(spacing: 15) {
                    Button(action: {
                        if #available(iOS 14.0, *) {
                            watermarkManager.takeScreenshotWithModernAPI(watermarkText: watermarkText)
                        } else {
                            watermarkManager.takeScreenshotWithWatermark(watermarkText: watermarkText)
                        }
                    }) {
                        HStack {
                            if watermarkManager.isProcessing {
                                ProgressView()
                                    .scaleEffect(0.8)
                            } else {
                                Image(systemName: "camera.fill")
                            }
                            Text(watermarkManager.isProcessing ? "处理中..." : "截图并添加水印")
                        }
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.blue)
                        .foregroundColor(.white)
                        .cornerRadius(10)
                    }
                    .disabled(watermarkManager.isProcessing)

                    // 添加测试按钮（用于模拟器测试）
                    Button(action: {
                        watermarkManager.createTestImageWithWatermark(watermarkText: watermarkText)
                    }) {
                        HStack {
                            if watermarkManager.isProcessing {
                                ProgressView()
                                    .scaleEffect(0.8)
                            } else {
                                Image(systemName: "photo.badge.plus")
                            }
                            Text(watermarkManager.isProcessing ? "处理中..." : "创建测试图片")
                        }
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.green)
                        .foregroundColor(.white)
                        .cornerRadius(10)
                    }
                    .disabled(watermarkManager.isProcessing)

                    Button(action: {
                        showCustomization.toggle()
                    }) {
                        HStack {
                            Image(systemName: "gear")
                            Text("自定义设置")
                        }
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color(.systemGray5))
                        .foregroundColor(.primary)
                        .cornerRadius(10)
                    }

                    NavigationLink(destination: WatermarkExampleView()) {
                        HStack {
                            Image(systemName: "photo.badge.plus")
                            Text("图片水印演示")
                        }
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color(.systemGray5))
                        .foregroundColor(.primary)
                        .cornerRadius(10)
                    }

                    NavigationLink(destination: SimpleWatermarkTest()) {
                        HStack {
                            Image(systemName: "checkmark.circle")
                            Text("简化测试版本")
                        }
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color(.systemGray5))
                        .foregroundColor(.primary)
                        .cornerRadius(10)
                    }

                    NavigationLink(destination: ScreenshotProtectionDemo()) {
                        HStack {
                            Image(systemName: "shield.fill")
                            Text("截屏保护演示")
                        }
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.green.opacity(0.1))
                        .foregroundColor(.green)
                        .cornerRadius(10)
                        .overlay(
                            RoundedRectangle(cornerRadius: 10)
                                .stroke(Color.green, lineWidth: 1)
                        )
                    }

                    NavigationLink(destination: ScreenshotProtectionTest()) {
                        HStack {
                            Image(systemName: "testtube.2")
                            Text("静默替换测试")
                        }
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.purple.opacity(0.1))
                        .foregroundColor(.purple)
                        .cornerRadius(10)
                        .overlay(
                            RoundedRectangle(cornerRadius: 10)
                                .stroke(Color.purple, lineWidth: 1)
                        )
                    }
                }

                Spacer()
            }
            .padding()
            .navigationTitle("水印工具")
            .navigationBarTitleDisplayMode(.inline)
        }
        .alert("提示", isPresented: $watermarkManager.showAlert) {
            Button("确定", role: .cancel) { }
        } message: {
            Text(watermarkManager.alertMessage)
        }
        .sheet(isPresented: $showCustomization) {
            WatermarkCustomizationView(watermarkText: $watermarkText)
        }
    }
}

// MARK: - 演示内容视图
struct DemoContentView: View {
    var body: some View {
        VStack(spacing: 15) {
            HStack {
                Image(systemName: "star.fill")
                    .foregroundColor(.yellow)
                Text("这是一个演示界面")
                    .font(.headline)
                Spacer()
            }

            HStack {
                VStack(alignment: .leading, spacing: 5) {
                    Text("功能特点:")
                        .font(.subheadline)
                        .fontWeight(.semibold)
                    Text("• 自动截屏")
                    Text("• 添加文字水印")
                    Text("• 保存到相册")
                    Text("• 自定义样式")
                    Text("• 🔒 截屏保护模式")
                        .foregroundColor(.green)
                    Text("• 🛡️ 敏感内容防护")
                        .foregroundColor(.green)
                }
                .font(.caption)
                .foregroundColor(.secondary)

                Spacer()

                VStack {
                    Circle()
                        .fill(Color.blue.opacity(0.3))
                        .frame(width: 50, height: 50)
                        .overlay(
                            Image(systemName: "checkmark")
                                .foregroundColor(.blue)
                        )
                }
            }
        }
    }
}

#Preview {
    ContentView()
}
