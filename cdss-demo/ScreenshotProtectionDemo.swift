//
//  ScreenshotProtectionDemo.swift
//  cdss-demo
//
//  Created by kit.yeung on 2025/7/31.
//

import SwiftUI

struct ScreenshotProtectionDemo: View {
    @StateObject private var screenshotInterceptor = ScreenshotInterceptor()
    @State private var showInstructions = false
    @State private var sensitiveData = [
        "用户ID: 12345678",
        "密码: ********",
        "银行卡号: **** **** **** 1234",
        "身份证号: 123456********1234",
        "手机号: 138****5678"
    ]
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // 标题区域
                    VStack(spacing: 10) {
                        Image(systemName: "shield.fill")
                            .font(.system(size: 50))
                            .foregroundColor(.green)
                        
                        Text("截屏保护演示")
                            .font(.title)
                            .fontWeight(.bold)
                        
                        Text("保护敏感信息不被截屏泄露")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }
                    .padding()
                    
                    // 保护开关
                    VStack(spacing: 15) {
                        HStack {
                            Text("截屏保护")
                                .font(.headline)
                            Spacer()
                            Toggle("", isOn: $screenshotInterceptor.isInterceptionEnabled)
                                .onChange(of: screenshotInterceptor.isInterceptionEnabled) { enabled in
                                    if enabled {
                                        screenshotInterceptor.enableScreenshotInterception()
                                    } else {
                                        screenshotInterceptor.disableScreenshotInterception()
                                    }
                                }
                        }
                        
                        HStack {
                            Image(systemName: screenshotInterceptor.isInterceptionEnabled ? "lock.fill" : "lock.open.fill")
                                .foregroundColor(screenshotInterceptor.isInterceptionEnabled ? .green : .red)
                            
                            Text(screenshotInterceptor.isInterceptionEnabled ?
                                 "🔒 保护已启用 - 截屏内容将被自动转换为安全内容" :
                                 "🔓 保护已关闭 - 截屏正常显示")
                                .font(.caption)
                                .foregroundColor(screenshotInterceptor.isInterceptionEnabled ? .green : .red)
                            
                            Spacer()
                        }
                    }
                    .padding()
                    .background(Color(.systemGray6))
                    .cornerRadius(12)
                    
                    // 敏感信息区域
                    VStack(alignment: .leading, spacing: 15) {
                        HStack {
                            Image(systemName: "exclamationmark.triangle.fill")
                                .foregroundColor(.orange)
                            Text("敏感信息区域")
                                .font(.headline)
                                .fontWeight(.bold)
                            Spacer()
                        }
                        
                        VStack(alignment: .leading, spacing: 10) {
                            ForEach(sensitiveData, id: \.self) { data in
                                HStack {
                                    Image(systemName: "key.fill")
                                        .foregroundColor(.blue)
                                        .frame(width: 20)
                                    Text(data)
                                        .font(.system(.body, design: .monospaced))
                                    Spacer()
                                }
                                .padding(.vertical, 5)
                            }
                        }
                        .padding()
                        .background(Color(.systemYellow).opacity(0.1))
                        .cornerRadius(8)
                        .overlay(
                            RoundedRectangle(cornerRadius: 8)
                                .stroke(Color.orange, lineWidth: 1)
                        )
                        
                        Text("⚠️ 以上信息在截屏保护模式下不会被真实截取")
                            .font(.caption)
                            .foregroundColor(.orange)
                            .italic()
                    }
                    .padding()
                    .background(Color(.systemBackground))
                    .cornerRadius(12)
                    .shadow(radius: 2)
                    
                    // 测试说明
                    VStack(alignment: .leading, spacing: 10) {
                        HStack {
                            Image(systemName: "info.circle.fill")
                                .foregroundColor(.blue)
                            Text("如何测试")
                                .font(.headline)
                                .fontWeight(.bold)
                            Spacer()
                        }
                        
                        VStack(alignment: .leading, spacing: 8) {
                            Text("1. 开启截屏保护开关")
                            Text("2. 使用系统截屏功能（音量上键+电源键）")
                            Text("3. 截屏操作正常完成，无任何异常提示")
                            Text("4. 查看相册中的截屏图片")
                            Text("5. 您会发现截屏内容已被自动转换为安全内容")
                            Text("6. 整个过程完全透明，无删除确认对话框")
                                .foregroundColor(.green)
                                .font(.caption)
                        }
                        .font(.body)
                        .foregroundColor(.secondary)
                        
                        Button(action: {
                            showInstructions.toggle()
                        }) {
                            HStack {
                                Image(systemName: "questionmark.circle")
                                Text("详细说明")
                            }
                            .foregroundColor(.blue)
                        }
                    }
                    .padding()
                    .background(Color(.systemGray6))
                    .cornerRadius(12)
                    
                    // 状态指示器
                    HStack {
                        Circle()
                            .fill(screenshotInterceptor.isInterceptionEnabled ? Color.green : Color.red)
                            .frame(width: 12, height: 12)
                        
                        Text(screenshotInterceptor.isInterceptionEnabled ? "保护模式已激活" : "保护模式未激活")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        Spacer()
                    }
                    .padding(.horizontal)
                    
                    Spacer()
                }
                .padding()
            }
            .navigationTitle("截屏保护")
            .navigationBarTitleDisplayMode(.inline)
        }
        .sheet(isPresented: $showInstructions) {
            ScreenshotProtectionInstructions()
        }
    }
}

// MARK: - 详细说明视图
struct ScreenshotProtectionInstructions: View {
    @Environment(\.presentationMode) var presentationMode
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 20) {
                    Text("截屏保护功能说明")
                        .font(.title2)
                        .fontWeight(.bold)
                        .padding(.bottom)
                    
                    VStack(alignment: .leading, spacing: 15) {
                        Text("工作原理")
                            .font(.headline)
                            .foregroundColor(.blue)
                        
                        Text("1. 监听系统截屏通知")
                        Text("2. 检测到截屏后立即获取最新截屏")
                        Text("3. 使用UIGraphicsImageRenderer生成安全内容")
                        Text("4. 删除原截屏并保存替换图片")
                    }
                    
                    VStack(alignment: .leading, spacing: 15) {
                        Text("安全特性")
                            .font(.headline)
                            .foregroundColor(.green)
                        
                        Text("• 实时监听截屏事件")
                        Text("• 自动替换敏感内容")
                        Text("• 保持截屏操作的用户体验")
                        Text("• 生成带水印的安全图片")
                    }
                    
                    VStack(alignment: .leading, spacing: 15) {
                        Text("注意事项")
                            .font(.headline)
                            .foregroundColor(.orange)
                        
                        Text("• 需要相册访问权限")
                        Text("• 仅在真机上有效（模拟器无法截屏）")
                        Text("• 替换过程可能有短暂延迟")
                        Text("• 建议在敏感页面启用此功能")
                    }
                    
                    Spacer()
                }
                .padding()
            }
            .navigationTitle("功能说明")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarItems(trailing: Button("关闭") {
                presentationMode.wrappedValue.dismiss()
            })
        }
    }
}

#Preview {
    ScreenshotProtectionDemo()
}
