# 截屏保护功能说明

## 功能概述

这个功能实现了iOS系统截屏事件的监听和自动替换，当用户进行系统截屏时，会自动将真实的截屏图片替换成使用UIGraphicsImageRenderer生成的安全演示内容。

## 核心特性

### 🔒 实时截屏监听
- 监听 `UIApplication.userDidTakeScreenshotNotification` 通知
- 实时检测系统截屏行为
- 自动触发替换流程

### 🛡️ 智能内容替换
- 使用 `UIGraphicsImageRenderer` 生成安全内容
- 保持原始截屏的尺寸和格式
- 添加安全提示和水印信息

### 📱 无缝用户体验
- 保持系统截屏的操作体验
- 自动处理相册权限
- 提供状态反馈

## 技术实现

### 核心类：ScreenshotInterceptor

```swift
class ScreenshotInterceptor: ObservableObject {
    @Published var isInterceptionEnabled = false
    private var screenshotObserver: NSObjectProtocol?
    
    // 监听截屏通知
    private func setupScreenshotInterception() {
        screenshotObserver = NotificationCenter.default.addObserver(
            forName: UIApplication.userDidTakeScreenshotNotification,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            self?.handleScreenshotTaken()
        }
    }
}
```

### 替换流程

1. **检测截屏事件**
   - 监听系统截屏通知
   - 验证保护模式是否启用

2. **获取最新截屏**
   - 查询相册中最新的截屏图片
   - 验证截屏时间（5秒内）

3. **生成替换内容**
   - 使用UIGraphicsImageRenderer创建安全图片
   - 添加应用信息和时间戳
   - 添加安全提示和水印

4. **执行替换操作**
   - 删除原始截屏
   - 保存新的安全图片

## 使用方法

### 1. 启用保护模式

```swift
@StateObject private var screenshotInterceptor = ScreenshotInterceptor()

// 启用保护
screenshotInterceptor.enableScreenshotInterception()

// 禁用保护
screenshotInterceptor.disableScreenshotInterception()
```

### 2. 在SwiftUI中集成

```swift
Toggle("截屏保护", isOn: $screenshotInterceptor.isInterceptionEnabled)
    .onChange(of: screenshotInterceptor.isInterceptionEnabled) { enabled in
        if enabled {
            screenshotInterceptor.enableScreenshotInterception()
        } else {
            screenshotInterceptor.disableScreenshotInterception()
        }
    }
```

## 安全特性

### 🔐 内容保护
- 敏感信息不会出现在截屏中
- 自动生成安全的演示内容
- 保持截屏操作的正常体验

### 🎨 自定义安全内容
- 应用名称和图标
- 当前时间戳
- 安全提示信息
- 自定义水印

### ⚡ 性能优化
- 异步处理替换操作
- 最小化UI阻塞
- 智能时间窗口检测

## 权限要求

### 相册访问权限
在Info.plist中添加：

```xml
<key>NSPhotoLibraryAddUsageDescription</key>
<string>需要访问相册来保存带水印的截图</string>
<key>NSPhotoLibraryUsageDescription</key>
<string>需要访问相册功能，用于保存截图</string>
```

## 测试方法

### 真机测试
1. 在真机上运行应用
2. 启用截屏保护模式
3. 使用系统截屏（音量上键+电源键）
4. 查看相册中的截屏结果

### 注意事项
- 模拟器无法进行系统截屏测试
- 需要相册访问权限
- 替换过程可能有1-2秒延迟

## 应用场景

### 🏦 金融应用
- 保护账户信息
- 隐藏交易记录
- 防止敏感数据泄露

### 🏥 医疗应用
- 保护患者隐私
- 隐藏医疗记录
- 符合HIPAA规范

### 💼 企业应用
- 保护商业机密
- 防止数据泄露
- 提高安全等级

## 扩展功能

### 自定义安全内容
可以根据应用需求自定义替换的安全内容：

```swift
private func generateCustomSecurityContent() -> UIImage {
    // 自定义安全内容生成逻辑
    // 可以包括公司Logo、免责声明等
}
```

### 选择性保护
可以针对特定页面或内容启用保护：

```swift
// 在敏感页面启用
.onAppear {
    screenshotInterceptor.enableScreenshotInterception()
}
.onDisappear {
    screenshotInterceptor.disableScreenshotInterception()
}
```

## 总结

这个截屏保护功能提供了一个完整的解决方案来保护iOS应用中的敏感信息不被截屏泄露。通过监听系统截屏事件并自动替换内容，既保护了用户隐私，又保持了良好的用户体验。
