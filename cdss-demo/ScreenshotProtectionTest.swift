//
//  ScreenshotProtectionTest.swift
//  cdss-demo
//
//  Created by kit.yeung on 2025/7/31.
//

import SwiftUI

struct ScreenshotProtectionTest: View {
    @StateObject private var screenshotInterceptor = ScreenshotInterceptor()
    @State private var testResults: [String] = []
    @State private var isTestingMode = false
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // 测试标题
                    VStack(spacing: 10) {
                        Image(systemName: "testtube.2")
                            .font(.system(size: 50))
                            .foregroundColor(.blue)
                        
                        Text("截屏保护功能测试")
                            .font(.title)
                            .fontWeight(.bold)
                        
                        Text("验证静默替换功能")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }
                    .padding()
                    
                    // 测试控制面板
                    VStack(spacing: 15) {
                        HStack {
                            Text("测试模式")
                                .font(.headline)
                            Spacer()
                            Toggle("", isOn: $isTestingMode)
                        }
                        
                        HStack {
                            Text("截屏保护")
                                .font(.headline)
                            Spacer()
                            Toggle("", isOn: $screenshotInterceptor.isInterceptionEnabled)
                                .onChange(of: screenshotInterceptor.isInterceptionEnabled) { enabled in
                                    if enabled {
                                        screenshotInterceptor.enableScreenshotInterception()
                                        addTestResult("✅ 截屏保护已启用")
                                    } else {
                                        screenshotInterceptor.disableScreenshotInterception()
                                        addTestResult("❌ 截屏保护已禁用")
                                    }
                                }
                        }
                    }
                    .padding()
                    .background(Color(.systemGray6))
                    .cornerRadius(12)
                    
                    // 测试内容区域
                    VStack(alignment: .leading, spacing: 15) {
                        HStack {
                            Image(systemName: "eye.slash.fill")
                                .foregroundColor(.red)
                            Text("敏感测试数据")
                                .font(.headline)
                                .fontWeight(.bold)
                            Spacer()
                        }
                        
                        VStack(alignment: .leading, spacing: 10) {
                            TestDataRow(icon: "creditcard.fill", title: "信用卡", data: "4532 1234 5678 9012", isVisible: !screenshotInterceptor.isInterceptionEnabled)
                            TestDataRow(icon: "person.fill", title: "身份证", data: "123456199001011234", isVisible: !screenshotInterceptor.isInterceptionEnabled)
                            TestDataRow(icon: "phone.fill", title: "手机号", data: "138-0013-8000", isVisible: !screenshotInterceptor.isInterceptionEnabled)
                            TestDataRow(icon: "key.fill", title: "密码", data: "MySecretPassword123!", isVisible: !screenshotInterceptor.isInterceptionEnabled)
                            TestDataRow(icon: "building.2.fill", title: "银行账户", data: "6222 0012 3456 7890", isVisible: !screenshotInterceptor.isInterceptionEnabled)
                        }
                        
                        if screenshotInterceptor.isInterceptionEnabled {
                            HStack {
                                Image(systemName: "shield.checkered")
                                    .foregroundColor(.green)
                                Text("以上敏感数据在截屏时将被自动保护")
                                    .font(.caption)
                                    .foregroundColor(.green)
                                Spacer()
                            }
                            .padding(.top, 10)
                        }
                    }
                    .padding()
                    .background(Color(.systemBackground))
                    .cornerRadius(12)
                    .shadow(radius: 2)
                    
                    // 测试说明
                    VStack(alignment: .leading, spacing: 10) {
                        HStack {
                            Image(systemName: "info.circle.fill")
                                .foregroundColor(.blue)
                            Text("测试步骤")
                                .font(.headline)
                                .fontWeight(.bold)
                            Spacer()
                        }
                        
                        VStack(alignment: .leading, spacing: 8) {
                            Text("1. 启用截屏保护开关")
                            Text("2. 使用系统截屏（音量上键+电源键）")
                            Text("3. 观察是否有删除确认对话框")
                                .foregroundColor(.orange)
                            Text("4. 检查相册中的截屏内容")
                            Text("5. 验证敏感数据是否被保护")
                        }
                        .font(.body)
                        .foregroundColor(.secondary)
                    }
                    .padding()
                    .background(Color(.systemGray6))
                    .cornerRadius(12)
                    
                    // 测试结果
                    if !testResults.isEmpty {
                        VStack(alignment: .leading, spacing: 10) {
                            HStack {
                                Image(systemName: "list.clipboard.fill")
                                    .foregroundColor(.purple)
                                Text("测试日志")
                                    .font(.headline)
                                    .fontWeight(.bold)
                                Spacer()
                                Button("清除") {
                                    testResults.removeAll()
                                }
                                .font(.caption)
                                .foregroundColor(.blue)
                            }
                            
                            ScrollView {
                                VStack(alignment: .leading, spacing: 5) {
                                    ForEach(testResults.indices, id: \.self) { index in
                                        HStack {
                                            Text("\(index + 1).")
                                                .font(.caption)
                                                .foregroundColor(.secondary)
                                            Text(testResults[index])
                                                .font(.caption)
                                            Spacer()
                                        }
                                    }
                                }
                            }
                            .frame(maxHeight: 150)
                        }
                        .padding()
                        .background(Color(.systemGray6))
                        .cornerRadius(12)
                    }
                    
                    Spacer()
                }
                .padding()
            }
            .navigationTitle("功能测试")
            .navigationBarTitleDisplayMode(.inline)
        }
        .onAppear {
            addTestResult("🧪 测试页面已加载")
        }
    }
    
    private func addTestResult(_ result: String) {
        let timestamp = DateFormatter.localizedString(from: Date(), dateStyle: .none, timeStyle: .medium)
        testResults.append("[\(timestamp)] \(result)")
    }
}

// MARK: - 测试数据行组件
struct TestDataRow: View {
    let icon: String
    let title: String
    let data: String
    let isVisible: Bool
    
    var body: some View {
        HStack {
            Image(systemName: icon)
                .foregroundColor(.blue)
                .frame(width: 20)
            
            Text(title + ":")
                .font(.system(.body, design: .default))
                .fontWeight(.medium)
                .frame(width: 60, alignment: .leading)
            
            if isVisible {
                Text(data)
                    .font(.system(.body, design: .monospaced))
                    .foregroundColor(.primary)
            } else {
                Text("●●●●●●●●●●●●")
                    .font(.system(.body, design: .monospaced))
                    .foregroundColor(.green)
            }
            
            Spacer()
            
            Image(systemName: isVisible ? "eye.fill" : "eye.slash.fill")
                .foregroundColor(isVisible ? .red : .green)
                .font(.caption)
        }
        .padding(.vertical, 5)
    }
}

#Preview {
    ScreenshotProtectionTest()
}
