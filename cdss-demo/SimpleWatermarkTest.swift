//
//  SimpleWatermarkTest.swift
//  cdss-demo
//
//  Created by kit.yeung on 2025/7/31.
//

import SwiftUI
import UIKit
import Photos

struct SimpleWatermarkTest: View {
    @State private var isProcessing = false
    @State private var showAlert = false
    @State private var alertMessage = ""
    @State private var watermarkText = "© 2024 测试水印"
    
    var body: some View {
        VStack(spacing: 30) {
            Text("简化水印测试")
                .font(.title)
                .fontWeight(.bold)
            
            TextField("水印文字", text: $watermarkText)
                .textFieldStyle(RoundedBorderTextFieldStyle())
                .padding(.horizontal)
            
            Button(action: {
                createSimpleWatermarkImage()
            }) {
                HStack {
                    if isProcessing {
                        ProgressView()
                            .scaleEffect(0.8)
                    } else {
                        Image(systemName: "photo.badge.plus")
                    }
                    Text(isProcessing ? "处理中..." : "创建水印图片")
                }
                .frame(maxWidth: .infinity)
                .padding()
                .background(Color.blue)
                .foregroundColor(.white)
                .cornerRadius(10)
            }
            .disabled(isProcessing)
            .padding(.horizontal)
            
            Text("这个测试版本不会截屏，而是创建一个带水印的演示图片")
                .font(.caption)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal)
            
            Spacer()
        }
        .padding()
        .alert("提示", isPresented: $showAlert) {
            Button("确定", role: .cancel) { }
        } message: {
            Text(alertMessage)
        }
    }
    
    private func createSimpleWatermarkImage() {
        isProcessing = true
        
        DispatchQueue.global(qos: .userInitiated).async {
            // 创建一个简单的图片
            let size = CGSize(width: 400, height: 600)
            let renderer = UIGraphicsImageRenderer(size: size)
            
            let image = renderer.image { context in
                // 绘制渐变背景
                let colors = [UIColor.systemBlue.cgColor, UIColor.systemTeal.cgColor]
                if let gradient = CGGradient(colorsSpace: CGColorSpaceCreateDeviceRGB(), 
                                           colors: colors as CFArray, 
                                           locations: nil) {
                    context.cgContext.drawLinearGradient(gradient, 
                                                       start: CGPoint(x: 0, y: 0), 
                                                       end: CGPoint(x: size.width, y: size.height), 
                                                       options: [])
                }
                
                // 添加主标题
                let title = "水印测试图片"
                let titleAttributes: [NSAttributedString.Key: Any] = [
                    .font: UIFont.systemFont(ofSize: 32, weight: .bold),
                    .foregroundColor: UIColor.white
                ]
                
                let titleSize = title.size(withAttributes: titleAttributes)
                let titleRect = CGRect(
                    x: (size.width - titleSize.width) / 2,
                    y: size.height / 3,
                    width: titleSize.width,
                    height: titleSize.height
                )
                title.draw(in: titleRect, withAttributes: titleAttributes)
                
                // 添加时间
                let formatter = DateFormatter()
                formatter.dateStyle = .medium
                formatter.timeStyle = .short
                let timeString = formatter.string(from: Date())
                
                let timeAttributes: [NSAttributedString.Key: Any] = [
                    .font: UIFont.systemFont(ofSize: 18),
                    .foregroundColor: UIColor.white.withAlphaComponent(0.8)
                ]
                
                let timeSize = timeString.size(withAttributes: timeAttributes)
                let timeRect = CGRect(
                    x: (size.width - timeSize.width) / 2,
                    y: size.height / 2,
                    width: timeSize.width,
                    height: timeSize.height
                )
                timeString.draw(in: timeRect, withAttributes: timeAttributes)
                
                // 添加水印
                self.addSimpleWatermark(to: size, context: context)
            }
            
            // 保存图片
            DispatchQueue.main.async {
                self.saveImageToPhotoLibrary(image)
            }
        }
    }
    
    private func addSimpleWatermark(to size: CGSize, context: UIGraphicsImageRendererContext) {
        // 水印文字属性
        let fontSize: CGFloat = size.width * 0.05
        let attributes: [NSAttributedString.Key: Any] = [
            .font: UIFont.systemFont(ofSize: fontSize, weight: .medium),
            .foregroundColor: UIColor.white
        ]
        
        let textSize = watermarkText.size(withAttributes: attributes)
        let padding: CGFloat = 8
        
        // 计算水印位置（右下角）
        let backgroundRect = CGRect(
            x: size.width - textSize.width - padding * 2 - 20,
            y: size.height - textSize.height - padding * 2 - 20,
            width: textSize.width + padding * 2,
            height: textSize.height + padding * 2
        )
        
        // 绘制半透明背景
        let backgroundColor = UIColor.black.withAlphaComponent(0.6)
        backgroundColor.setFill()
        let backgroundPath = UIBezierPath(roundedRect: backgroundRect, cornerRadius: 8)
        backgroundPath.fill()
        
        // 绘制水印文字
        let textRect = CGRect(
            x: backgroundRect.origin.x + padding,
            y: backgroundRect.origin.y + padding,
            width: textSize.width,
            height: textSize.height
        )
        
        watermarkText.draw(in: textRect, withAttributes: attributes)
    }
    
    private func saveImageToPhotoLibrary(_ image: UIImage) {
        PHPhotoLibrary.requestAuthorization { status in
            DispatchQueue.main.async {
                self.isProcessing = false

                switch status {
                case .authorized, .limited:
                    // 使用PHPhotoLibrary的现代API
                    self.saveImageWithModernAPI(image)
                case .denied, .restricted:
                    self.showError("需要相册访问权限才能保存图片")
                case .notDetermined:
                    self.showError("相册权限未确定")
                @unknown default:
                    self.showError("未知的权限状态")
                }
            }
        }
    }

    private func saveImageWithModernAPI(_ image: UIImage) {
        PHPhotoLibrary.shared().performChanges({
            PHAssetChangeRequest.creationRequestForAsset(from: image)
        }) { success, error in
            DispatchQueue.main.async {
                if success {
                    self.showSuccess("图片已保存到相册")
                } else if let error = error {
                    self.showError("保存失败: \(error.localizedDescription)")
                } else {
                    self.showError("保存失败：未知错误")
                }
            }
        }
    }
    
    private func showError(_ message: String) {
        alertMessage = message
        showAlert = true
    }
    
    private func showSuccess(_ message: String) {
        alertMessage = message
        showAlert = true
    }
}

#Preview {
    SimpleWatermarkTest()
}
