//
//  WatermarkManager.swift
//  cdss-demo
//
//  Created by kit.yeung on 2025/7/31.
//

import SwiftUI
import UIKit
import Photos
import PhotosUI

// MARK: - 截屏监听管理器
class ScreenshotInterceptor: ObservableObject {
    @Published var isInterceptionEnabled = false
    @Published var lastOperationStatus = "待机中..."
    @Published var permissionStatus = "未检查"
    private var screenshotObserver: NSObjectProtocol?
    private let watermarkManager = WatermarkManager()

    init() {
        setupScreenshotInterception()
        checkPermissionStatus()
    }

    // MARK: - 检查权限状态
    private func checkPermissionStatus() {
        let status = PHPhotoLibrary.authorizationStatus()
        DispatchQueue.main.async { [weak self] in
            switch status {
            case .authorized:
                self?.permissionStatus = "✅ 已授权"
            case .limited:
                self?.permissionStatus = "⚠️ 受限访问"
            case .denied:
                self?.permissionStatus = "❌ 已拒绝"
            case .restricted:
                self?.permissionStatus = "🚫 受限制"
            case .notDetermined:
                self?.permissionStatus = "❓ 未确定"
            @unknown default:
                self?.permissionStatus = "❓ 未知状态"
            }
        }
    }

    deinit {
        stopScreenshotInterception()
    }

    // MARK: - 启用截屏拦截
    func enableScreenshotInterception() {
        isInterceptionEnabled = true
        setupScreenshotInterception()
    }

    // MARK: - 禁用截屏拦截
    func disableScreenshotInterception() {
        isInterceptionEnabled = false
        stopScreenshotInterception()
    }

    // MARK: - 设置截屏监听
    private func setupScreenshotInterception() {
        // 移除之前的监听器
        stopScreenshotInterception()

        // 监听截屏通知
        screenshotObserver = NotificationCenter.default.addObserver(
            forName: UIApplication.userDidTakeScreenshotNotification,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            self?.handleScreenshotTaken()
        }
    }

    // MARK: - 停止截屏监听
    private func stopScreenshotInterception() {
        if let observer = screenshotObserver {
            NotificationCenter.default.removeObserver(observer)
            screenshotObserver = nil
        }
    }

    // MARK: - 处理截屏事件
    private func handleScreenshotTaken() {
        guard isInterceptionEnabled else { return }

        DispatchQueue.main.async { [weak self] in
            self?.lastOperationStatus = "🔒 检测到截屏，启动保护..."
        }

        print("🔒 检测到系统截屏，启动内容保护...")

        // 延迟更长时间，确保系统截屏完全保存
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) { [weak self] in
            self?.replaceLatestScreenshot()
        }
    }

    // MARK: - 替换最新的截屏
    private func replaceLatestScreenshot() {
        // 检查当前权限状态
        let currentStatus = PHPhotoLibrary.authorizationStatus()

        switch currentStatus {
        case .authorized:
            print("📱 相册权限已授权，开始处理截屏...")
            DispatchQueue.main.async { [weak self] in
                self?.lastOperationStatus = "📱 权限已授权，处理中..."
            }
            performScreenshotReplacement()
        case .limited:
            print("📱 相册权限受限，尝试处理截屏...")
            DispatchQueue.main.async { [weak self] in
                self?.lastOperationStatus = "⚠️ 权限受限，尝试处理..."
            }
            performScreenshotReplacement()
        case .denied, .restricted:
            print("❌ 相册权限被拒绝，无法处理截屏")
            DispatchQueue.main.async { [weak self] in
                self?.lastOperationStatus = "❌ 权限不足，使用回退方案"
            }
            // 直接创建新的安全图片作为替代
            let safeImage = generateReplacementScreenshot()
            fallbackToCreateNewImage(safeImage)
        case .notDetermined:
            print("📋 请求相册权限...")
            // 请求权限
            PHPhotoLibrary.requestAuthorization { [weak self] status in
                DispatchQueue.main.async {
                    switch status {
                    case .authorized, .limited:
                        self?.performScreenshotReplacement()
                    default:
                        print("❌ 权限请求被拒绝")
                        let safeImage = self?.generateReplacementScreenshot()
                        if let image = safeImage {
                            self?.fallbackToCreateNewImage(image)
                        }
                    }
                }
            }
        @unknown default:
            print("❓ 未知的权限状态")
            let safeImage = generateReplacementScreenshot()
            fallbackToCreateNewImage(safeImage)
        }
    }

    // MARK: - 执行截屏替换
    private func performScreenshotReplacement() {
        // 获取最新的截屏
        let fetchOptions = PHFetchOptions()
        fetchOptions.sortDescriptors = [NSSortDescriptor(key: "creationDate", ascending: false)]
        fetchOptions.fetchLimit = 1
        fetchOptions.predicate = NSPredicate(format: "mediaSubtype == %d", PHAssetMediaSubtype.photoScreenshot.rawValue)

        let screenshots = PHAsset.fetchAssets(with: .image, options: fetchOptions)

        guard let latestScreenshot = screenshots.firstObject else {
            print("❌ 未找到最新的截屏")
            // 创建一个新的安全图片作为补偿
            let safeImage = generateReplacementScreenshot()
            fallbackToCreateNewImage(safeImage)
            return
        }

        // 检查截屏是否是刚刚拍摄的（扩大时间窗口到10秒）
        let now = Date()
        if let creationDate = latestScreenshot.creationDate {
            let timeInterval = now.timeIntervalSince(creationDate)
            print("📅 截屏时间差: \(timeInterval)秒")

            if timeInterval > 10 {
                print("⏰ 截屏时间过久(\(timeInterval)秒)，跳过替换")
                return
            }
        }

        print("🎯 找到目标截屏，开始替换...")

        // 生成替换图片
        let replacementImage = generateReplacementScreenshot()

        // 尝试编辑原截屏内容，如果失败则使用回退方案
        editScreenshotAsset(latestScreenshot, with: replacementImage)
    }

    // MARK: - 生成替换截屏
    private func generateReplacementScreenshot() -> UIImage {
        guard let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene else {
            return createFallbackImage()
        }

        let renderer = UIGraphicsImageRenderer(size: windowScene.screen.bounds.size)
        let screenshot = renderer.image { context in
            // 创建一个安全的演示内容
            let bounds = windowScene.screen.bounds

            // 绘制背景
            UIColor.systemBackground.setFill()
            context.fill(bounds)

            // 添加应用内容的模拟
            let appName = Bundle.main.infoDictionary?["CFBundleName"] as? String ?? "CDSS Demo"
            let titleAttributes: [NSAttributedString.Key: Any] = [
                .font: UIFont.systemFont(ofSize: 28, weight: .bold),
                .foregroundColor: UIColor.label
            ]

            let titleSize = appName.size(withAttributes: titleAttributes)
            let titleRect = CGRect(
                x: (bounds.width - titleSize.width) / 2,
                y: bounds.height / 3,
                width: titleSize.width,
                height: titleSize.height
            )
            appName.draw(in: titleRect, withAttributes: titleAttributes)

            // 添加时间戳
            let formatter = DateFormatter()
            formatter.dateStyle = .medium
            formatter.timeStyle = .short
            let timestamp = formatter.string(from: Date())

            let timestampAttributes: [NSAttributedString.Key: Any] = [
                .font: UIFont.systemFont(ofSize: 16),
                .foregroundColor: UIColor.secondaryLabel
            ]

            let timestampSize = timestamp.size(withAttributes: timestampAttributes)
            let timestampRect = CGRect(
                x: (bounds.width - timestampSize.width) / 2,
                y: bounds.height / 2,
                width: timestampSize.width,
                height: timestampSize.height
            )
            timestamp.draw(in: timestampRect, withAttributes: timestampAttributes)

            // 添加安全提示
            let securityText = "🔒 安全截屏模式"
            let securityAttributes: [NSAttributedString.Key: Any] = [
                .font: UIFont.systemFont(ofSize: 14, weight: .medium),
                .foregroundColor: UIColor.systemGreen
            ]

            let securitySize = securityText.size(withAttributes: securityAttributes)
            let securityRect = CGRect(
                x: (bounds.width - securitySize.width) / 2,
                y: bounds.height * 0.7,
                width: securitySize.width,
                height: securitySize.height
            )
            securityText.draw(in: securityRect, withAttributes: securityAttributes)
        }

        // 添加水印
        return watermarkManager.addWatermarkToImage(screenshot, watermarkText: "© 2024 CDSS Demo - 安全截屏")
    }

    // MARK: - 创建备用图片
    private func createFallbackImage() -> UIImage {
        let size = CGSize(width: 375, height: 812)
        let renderer = UIGraphicsImageRenderer(size: size)

        return renderer.image { context in
            // 绘制渐变背景
            let colors = [UIColor.systemBlue.cgColor, UIColor.systemPurple.cgColor]
            let gradient = CGGradient(colorsSpace: CGColorSpaceCreateDeviceRGB(), colors: colors as CFArray, locations: nil)!
            context.cgContext.drawLinearGradient(gradient,
                                               start: CGPoint(x: 0, y: 0),
                                               end: CGPoint(x: size.width, y: size.height),
                                               options: [])

            // 添加安全提示文字
            let title = "🔒 安全截屏"
            let subtitle = "敏感内容已被保护"

            let titleAttributes: [NSAttributedString.Key: Any] = [
                .font: UIFont.systemFont(ofSize: 32, weight: .bold),
                .foregroundColor: UIColor.white
            ]

            let subtitleAttributes: [NSAttributedString.Key: Any] = [
                .font: UIFont.systemFont(ofSize: 18, weight: .medium),
                .foregroundColor: UIColor.white.withAlphaComponent(0.8)
            ]

            let titleSize = title.size(withAttributes: titleAttributes)
            let subtitleSize = subtitle.size(withAttributes: subtitleAttributes)

            let titleRect = CGRect(
                x: (size.width - titleSize.width) / 2,
                y: size.height / 2 - 30,
                width: titleSize.width,
                height: titleSize.height
            )

            let subtitleRect = CGRect(
                x: (size.width - subtitleSize.width) / 2,
                y: size.height / 2 + 10,
                width: subtitleSize.width,
                height: subtitleSize.height
            )

            title.draw(in: titleRect, withAttributes: titleAttributes)
            subtitle.draw(in: subtitleRect, withAttributes: subtitleAttributes)
        }
    }

    // MARK: - 编辑截屏资源（静默替换内容）
    private func editScreenshotAsset(_ asset: PHAsset, with newImage: UIImage) {
        print("🔧 尝试编辑截屏资源...")

        // 检查资源是否可编辑
        guard asset.canPerform(.content) else {
            print("❌ 截屏资源不支持内容编辑，使用回退方案")
            fallbackToCreateNewImage(newImage)
            return
        }

        // 请求编辑权限
        let options = PHContentEditingInputRequestOptions()
        options.isNetworkAccessAllowed = true

        asset.requestContentEditingInput(with: options) { [weak self] (contentEditingInput, info) in
            guard let input = contentEditingInput else {
                print("❌ 无法获取编辑输入，错误信息: \(info)")
                // 如果编辑失败，回退到创建新图片的方式
                DispatchQueue.main.async {
                    self?.fallbackToCreateNewImage(newImage)
                }
                return
            }

            print("✅ 获取编辑输入成功")

            // 创建编辑输出
            let editingOutput = PHContentEditingOutput(contentEditingInput: input)

            // 将新图片保存到临时位置
            guard let imageData = newImage.jpegData(compressionQuality: 0.9) else {
                print("❌ 无法生成图片数据")
                DispatchQueue.main.async {
                    self?.fallbackToCreateNewImage(newImage)
                }
                return
            }

            do {
                try imageData.write(to: editingOutput.renderedContentURL)
                print("✅ 临时文件写入成功")

                // 提交编辑更改
                PHPhotoLibrary.shared().performChanges({
                    let request = PHAssetChangeRequest(for: asset)
                    request.contentEditingOutput = editingOutput
                }) { success, error in
                    DispatchQueue.main.async {
                        if success {
                            print("🎉 截屏内容编辑成功 - 用户无感知替换完成")
                        } else {
                            let errorMsg = error?.localizedDescription ?? "未知错误"
                            let errorCode = (error as NSError?)?.code ?? -1
                            print("❌ 截屏编辑失败: \(errorMsg) (错误代码: \(errorCode))")

                            // 编辑失败时回退到创建新图片
                            self?.fallbackToCreateNewImage(newImage)
                        }
                    }
                }
            } catch {
                print("❌ 保存编辑内容失败: \(error.localizedDescription)")
                DispatchQueue.main.async {
                    self?.fallbackToCreateNewImage(newImage)
                }
            }
        }
    }

    // MARK: - 回退方案：创建新图片（当编辑失败时）
    private func fallbackToCreateNewImage(_ image: UIImage) {
        print("🔄 启用回退方案：创建新的安全图片...")

        PHPhotoLibrary.shared().performChanges({
            PHAssetChangeRequest.creationRequestForAsset(from: image)
        }) { success, error in
            DispatchQueue.main.async {
                if success {
                    print("✅ 回退方案成功：安全图片已创建并保存到相册")
                } else {
                    let errorMsg = error?.localizedDescription ?? "未知错误"
                    let errorCode = (error as NSError?)?.code ?? -1
                    print("❌ 回退方案失败: \(errorMsg) (错误代码: \(errorCode))")
                }
            }
        }
    }
}

@available(iOS 14.0, *)
extension WatermarkManager {
    // MARK: - 使用现代API的截屏方法
    func takeScreenshotWithModernAPI(watermarkText: String = "© 2024 CDSS Demo") {
        isProcessing = true

        // 使用iOS 14+的截屏API
        if #available(iOS 14.0, *) {
            DispatchQueue.main.async { [weak self] in
                self?.captureScreenWithModernAPI(watermarkText: watermarkText)
            }
        } else {
            // 降级到创建测试图片
            createTestImageWithWatermark(watermarkText: watermarkText)
        }
    }

    @available(iOS 14.0, *)
    private func captureScreenWithModernAPI(watermarkText: String) {
        guard let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene else {
            createTestImageWithWatermark(watermarkText: watermarkText)
            return
        }

        // 尝试获取屏幕内容
        let renderer = UIGraphicsImageRenderer(size: windowScene.screen.bounds.size)
        let screenshot = renderer.image { context in
            // 创建一个安全的演示内容
            let bounds = windowScene.screen.bounds

            // 绘制背景
            UIColor.systemBackground.setFill()
            context.fill(bounds)

            // 添加应用内容的模拟
            let appName = Bundle.main.infoDictionary?["CFBundleName"] as? String ?? "CDSS Demo"
            let titleAttributes: [NSAttributedString.Key: Any] = [
                .font: UIFont.systemFont(ofSize: 28, weight: .bold),
                .foregroundColor: UIColor.label
            ]

            let titleSize = appName.size(withAttributes: titleAttributes)
            let titleRect = CGRect(
                x: (bounds.width - titleSize.width) / 2,
                y: bounds.height / 3,
                width: titleSize.width,
                height: titleSize.height
            )
            appName.draw(in: titleRect, withAttributes: titleAttributes)

            // 添加时间戳
            let formatter = DateFormatter()
            formatter.dateStyle = .medium
            formatter.timeStyle = .short
            let timestamp = formatter.string(from: Date())

            let timestampAttributes: [NSAttributedString.Key: Any] = [
                .font: UIFont.systemFont(ofSize: 16),
                .foregroundColor: UIColor.secondaryLabel
            ]

            let timestampSize = timestamp.size(withAttributes: timestampAttributes)
            let timestampRect = CGRect(
                x: (bounds.width - timestampSize.width) / 2,
                y: bounds.height / 2,
                width: timestampSize.width,
                height: timestampSize.height
            )
            timestamp.draw(in: timestampRect, withAttributes: timestampAttributes)
        }

        processScreenshot(screenshot, watermarkText: watermarkText, logoName: nil)
    }
}

class WatermarkManager: ObservableObject {
    @Published var isProcessing = false
    @Published var showAlert = false
    @Published var alertMessage = ""
    
    // MARK: - 截屏并添加水印
    func takeScreenshotWithWatermark(watermarkText: String = "© 2024 CDSS Demo", logoName: String? = nil) {
        isProcessing = true

        // 使用更安全的截屏方法
        DispatchQueue.main.async { [weak self] in
            self?.captureScreenshot(watermarkText: watermarkText, logoName: logoName)
        }
    }

    // MARK: - 安全的截屏实现
    private func captureScreenshot(watermarkText: String, logoName: String?) {
        // 方法1：尝试使用窗口截屏
        if let screenshot = captureWindowScreenshot() {
            processScreenshot(screenshot, watermarkText: watermarkText, logoName: logoName)
            return
        }

        // 方法2：如果窗口截屏失败，创建一个演示图片
        let demoImage = createDemoImage()
        processScreenshot(demoImage, watermarkText: watermarkText, logoName: logoName)
    }

    // MARK: - 窗口截屏
    private func captureWindowScreenshot() -> UIImage? {
        guard let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
              let window = windowScene.windows.first else {
            return nil
        }

        // 使用更安全的截屏方法
        let renderer = UIGraphicsImageRenderer(size: window.bounds.size)

        return renderer.image { context in
            // 尝试安全的绘制方法
            if window.responds(to: #selector(UIView.drawHierarchy(in:afterScreenUpdates:))) {
                do {
                    window.drawHierarchy(in: window.bounds, afterScreenUpdates: false)
                } catch {
                    // 如果出错，绘制一个简单的背景
                    UIColor.systemBackground.setFill()
                    context.fill(window.bounds)

                    // 添加一些演示内容
                    let text = "截屏演示"
                    let attributes: [NSAttributedString.Key: Any] = [
                        .font: UIFont.systemFont(ofSize: 24, weight: .medium),
                        .foregroundColor: UIColor.label
                    ]
                    let textSize = text.size(withAttributes: attributes)
                    let textRect = CGRect(
                        x: (window.bounds.width - textSize.width) / 2,
                        y: (window.bounds.height - textSize.height) / 2,
                        width: textSize.width,
                        height: textSize.height
                    )
                    text.draw(in: textRect, withAttributes: attributes)
                }
            }
        }
    }

    // MARK: - 创建演示图片
    private func createDemoImage() -> UIImage {
        let size = CGSize(width: 375, height: 812) // iPhone标准尺寸
        let renderer = UIGraphicsImageRenderer(size: size)

        return renderer.image { context in
            // 绘制渐变背景
            let colors = [UIColor.systemBlue.cgColor, UIColor.systemPurple.cgColor]
            let gradient = CGGradient(colorsSpace: CGColorSpaceCreateDeviceRGB(), colors: colors as CFArray, locations: nil)!
            context.cgContext.drawLinearGradient(gradient,
                                               start: CGPoint(x: 0, y: 0),
                                               end: CGPoint(x: size.width, y: size.height),
                                               options: [])

            // 添加演示文字
            let title = "截屏演示"
            let subtitle = "这是一个演示截图"

            let titleAttributes: [NSAttributedString.Key: Any] = [
                .font: UIFont.systemFont(ofSize: 32, weight: .bold),
                .foregroundColor: UIColor.white
            ]

            let subtitleAttributes: [NSAttributedString.Key: Any] = [
                .font: UIFont.systemFont(ofSize: 18, weight: .medium),
                .foregroundColor: UIColor.white.withAlphaComponent(0.8)
            ]

            let titleSize = title.size(withAttributes: titleAttributes)
            let subtitleSize = subtitle.size(withAttributes: subtitleAttributes)

            let titleRect = CGRect(
                x: (size.width - titleSize.width) / 2,
                y: size.height / 2 - 30,
                width: titleSize.width,
                height: titleSize.height
            )

            let subtitleRect = CGRect(
                x: (size.width - subtitleSize.width) / 2,
                y: size.height / 2 + 10,
                width: subtitleSize.width,
                height: subtitleSize.height
            )

            title.draw(in: titleRect, withAttributes: titleAttributes)
            subtitle.draw(in: subtitleRect, withAttributes: subtitleAttributes)
        }
    }

    // MARK: - 处理截屏结果
    private func processScreenshot(_ screenshot: UIImage, watermarkText: String, logoName: String?) {
        // 添加水印
        let watermarkedImage = addWatermarkToImage(screenshot,
                                                 watermarkText: watermarkText,
                                                 logoName: logoName)

        // 保存到相册
        saveImageToPhotoLibrary(watermarkedImage)
    }

    // MARK: - 创建测试图片（用于模拟器测试）
    func createTestImageWithWatermark(watermarkText: String = "© 2024 CDSS Demo") {
        isProcessing = true

        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }

            // 创建一个测试图片
            let testImage = self.createDemoImage()

            // 添加水印
            let watermarkedImage = self.addWatermarkToImage(testImage,
                                                          watermarkText: watermarkText,
                                                          logoName: nil)

            // 保存到相册
            self.saveImageToPhotoLibrary(watermarkedImage)
        }
    }
    
    // MARK: - 给图片添加水印
    func addWatermarkToImage(_ originalImage: UIImage, 
                           watermarkText: String, 
                           logoName: String? = nil) -> UIImage {
        
        let renderer = UIGraphicsImageRenderer(size: originalImage.size)
        
        return renderer.image { context in
            // 绘制原始图片
            originalImage.draw(in: CGRect(origin: .zero, size: originalImage.size))
            
            // 添加Logo水印（如果提供）
            if let logoName = logoName, let logoImage = UIImage(named: logoName) {
                addLogoWatermark(logoImage, to: originalImage.size)
            }
            
            // 添加文字水印
            addTextWatermark(watermarkText, to: originalImage.size)
        }
    }
    
    // MARK: - 添加Logo水印
    private func addLogoWatermark(_ logoImage: UIImage, to imageSize: CGSize) {
        let logoSize = CGSize(
            width: imageSize.width * 0.15,  // Logo占图片宽度的15%
            height: imageSize.width * 0.15 * logoImage.size.height / logoImage.size.width
        )
        
        let logoRect = CGRect(
            x: imageSize.width - logoSize.width - 20,
            y: 20,
            width: logoSize.width,
            height: logoSize.height
        )
        
        logoImage.draw(in: logoRect)
    }
    
    // MARK: - 添加文字水印
    private func addTextWatermark(_ text: String, to imageSize: CGSize) {
        addTextWatermark(text,
                        to: imageSize,
                        position: .bottomRight,
                        style: .withBackground,
                        fontSize: imageSize.width * 0.04,
                        opacity: 0.8,
                        color: .white)
    }

    // MARK: - 添加自定义文字水印
    func addTextWatermark(_ text: String,
                         to imageSize: CGSize,
                         position: WatermarkPosition = .bottomRight,
                         style: WatermarkStyle = .withBackground,
                         fontSize: CGFloat = 16,
                         opacity: CGFloat = 0.8,
                         color: UIColor = .white) {

        // 文字属性
        let attributes: [NSAttributedString.Key: Any] = [
            .font: UIFont.systemFont(ofSize: fontSize, weight: .medium),
            .foregroundColor: color.withAlphaComponent(opacity),
            .strokeColor: UIColor.black.withAlphaComponent(0.8),
            .strokeWidth: style == .custom ? -1.0 : 0
        ]

        let textSize = text.size(withAttributes: attributes)
        let padding: CGFloat = 8

        // 计算位置
        let rect = calculateWatermarkRect(for: textSize,
                                        in: imageSize,
                                        position: position,
                                        padding: padding)

        // 根据样式绘制
        switch style {
        case .simple:
            text.draw(in: rect, withAttributes: attributes)

        case .withBackground, .withLogo:
            // 绘制背景
            let backgroundRect = CGRect(
                x: rect.origin.x - padding,
                y: rect.origin.y - padding,
                width: rect.width + padding * 2,
                height: rect.height + padding * 2
            )

            let backgroundColor = UIColor.black.withAlphaComponent(0.6)
            backgroundColor.setFill()
            let backgroundPath = UIBezierPath(roundedRect: backgroundRect, cornerRadius: 8)
            backgroundPath.fill()

            // 绘制文字
            if style == .withLogo {
                // 添加图标
                let iconSize: CGFloat = fontSize * 0.8
                let iconRect = CGRect(x: rect.origin.x - iconSize - 4,
                                    y: rect.origin.y + (rect.height - iconSize) / 2,
                                    width: iconSize,
                                    height: iconSize)

                if let starImage = UIImage(systemName: "star.fill") {
                    starImage.withTintColor(color.withAlphaComponent(opacity)).draw(in: iconRect)
                }
            }

            text.draw(in: rect, withAttributes: attributes)

        case .custom:
            // 自定义样式：带阴影的文字
            let shadowAttributes: [NSAttributedString.Key: Any] = [
                .font: UIFont.systemFont(ofSize: fontSize, weight: .bold),
                .foregroundColor: UIColor.black.withAlphaComponent(0.5),
                .strokeWidth: 0
            ]

            // 绘制阴影
            let shadowRect = CGRect(x: rect.origin.x + 1,
                                  y: rect.origin.y + 1,
                                  width: rect.width,
                                  height: rect.height)
            text.draw(in: shadowRect, withAttributes: shadowAttributes)

            // 绘制主文字
            let mainAttributes: [NSAttributedString.Key: Any] = [
                .font: UIFont.systemFont(ofSize: fontSize, weight: .bold),
                .foregroundColor: color.withAlphaComponent(opacity)
            ]
            text.draw(in: rect, withAttributes: mainAttributes)
        }
    }

    // MARK: - 计算水印位置
    private func calculateWatermarkRect(for textSize: CGSize,
                                      in imageSize: CGSize,
                                      position: WatermarkPosition,
                                      padding: CGFloat) -> CGRect {
        let margin: CGFloat = 20

        switch position {
        case .topLeft:
            return CGRect(x: margin, y: margin, width: textSize.width, height: textSize.height)
        case .topRight:
            return CGRect(x: imageSize.width - textSize.width - margin,
                         y: margin,
                         width: textSize.width,
                         height: textSize.height)
        case .bottomLeft:
            return CGRect(x: margin,
                         y: imageSize.height - textSize.height - margin,
                         width: textSize.width,
                         height: textSize.height)
        case .bottomRight:
            return CGRect(x: imageSize.width - textSize.width - margin,
                         y: imageSize.height - textSize.height - margin,
                         width: textSize.width,
                         height: textSize.height)
        case .center:
            return CGRect(x: (imageSize.width - textSize.width) / 2,
                         y: (imageSize.height - textSize.height) / 2,
                         width: textSize.width,
                         height: textSize.height)
        }
    }
    
    // MARK: - 保存图片到相册
    func saveImageToPhotoLibrary(_ image: UIImage) {
        PHPhotoLibrary.requestAuthorization { [weak self] status in
            DispatchQueue.main.async {
                switch status {
                case .authorized, .limited:
                    self?.saveImageWithModernAPI(image)
                case .denied, .restricted:
                    self?.showError("需要相册访问权限才能保存截图")
                case .notDetermined:
                    self?.showError("相册权限未确定")
                @unknown default:
                    self?.showError("未知的权限状态")
                }
            }
        }
    }

    // MARK: - 使用现代API保存图片
    private func saveImageWithModernAPI(_ image: UIImage) {
        PHPhotoLibrary.shared().performChanges({
            PHAssetChangeRequest.creationRequestForAsset(from: image)
        }) { [weak self] success, error in
            DispatchQueue.main.async {
                self?.isProcessing = false

                if success {
                    self?.showSuccess("图片已保存到相册")
                } else if let error = error {
                    self?.showError("保存失败: \(error.localizedDescription)")
                } else {
                    self?.showError("保存失败：未知错误")
                }
            }
        }
    }
    
    // MARK: - 显示错误信息
    private func showError(_ message: String) {
        alertMessage = message
        showAlert = true
    }
    
    // MARK: - 显示成功信息
    private func showSuccess(_ message: String) {
        alertMessage = message
        showAlert = true
    }
}

// MARK: - 水印样式枚举
enum WatermarkStyle {
    case simple          // 简单文字水印
    case withBackground  // 带背景的文字水印
    case withLogo       // 带Logo的水印
    case custom         // 自定义样式
}

// MARK: - 水印位置枚举
enum WatermarkPosition {
    case topLeft
    case topRight
    case bottomLeft
    case bottomRight
    case center
}
