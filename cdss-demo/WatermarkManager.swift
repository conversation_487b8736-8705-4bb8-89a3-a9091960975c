//
//  WatermarkManager.swift
//  cdss-demo
//
//  Created by kit.yeung on 2025/7/31.
//

import SwiftUI
import UIKit
import Photos
import PhotosUI

// MARK: - 多层截屏检测管理器
class ScreenshotInterceptor: ObservableObject {
    @Published var isInterceptionEnabled = false
    @Published var lastOperationStatus = "待机中..."
    @Published var isProtectionOverlayVisible = false
    @Published var isPreventiveMode = false // 预防性保护模式
    @Published var screenCaptureStatus = "未检测" // 屏幕捕获状态

    private var screenshotObserver: NSObjectProtocol?
    private var appStateObserver: NSObjectProtocol?
    private var screenCaptureObserver: NSObjectProtocol?
    private var protectionWindow: UIWindow?
    private let watermarkManager = WatermarkManager()

    // 检测状态
    private var isAppActive = true
    private var lastActiveTime = Date()
    private var screenCaptureDetectionTimer: Timer?
    private var isScreenBeingCaptured = false

    init() {
        setupMultiLayerDetection()
        initializeScreenCaptureStatus()
    }

    // MARK: - 初始化屏幕捕获状态
    private func initializeScreenCaptureStatus() {
        if #available(iOS 11.0, *) {
            updateScreenCaptureStatus()
        } else {
            DispatchQueue.main.async { [weak self] in
                self?.screenCaptureStatus = "⚠️ 需要iOS 11+"
            }
        }
    }

    deinit {
        stopAllDetection()
        cleanupProtectionWindow()
    }

    // MARK: - 清理保护窗口
    private func cleanupProtectionWindow() {
        protectionWindow?.isHidden = true
        protectionWindow = nil
    }

    // MARK: - 启用截屏拦截
    func enableScreenshotInterception() {
        isInterceptionEnabled = true
        lastOperationStatus = "🛡️ 多层保护模式已启用"
        setupMultiLayerDetection()
    }

    // MARK: - 禁用截屏拦截
    func disableScreenshotInterception() {
        isInterceptionEnabled = false
        lastOperationStatus = "⏸️ 保护模式已禁用"
        stopAllDetection()
        hideProtectionOverlay()
    }

    // MARK: - 切换预防性保护模式
    func togglePreventiveMode() {
        isPreventiveMode.toggle()
        if isPreventiveMode {
            lastOperationStatus = "🔒 预防性保护已启用"
            if isInterceptionEnabled {
                showProtectionOverlay()
            }
        } else {
            lastOperationStatus = "🔓 预防性保护已禁用"
            if !isProtectionOverlayVisible {
                hideProtectionOverlay()
            }
        }
    }

    // MARK: - 设置多层检测系统
    private func setupMultiLayerDetection() {
        stopAllDetection()

        // 1. 应用生命周期监听（最早期检测）
        setupAppStateDetection()

        // 2. 屏幕录制检测
        setupScreenCaptureDetection()

        // 3. 传统截屏通知（后备检测）
        setupScreenshotNotification()

        print("🔍 多层截屏检测系统已启动")
    }

    // MARK: - 应用状态检测
    private func setupAppStateDetection() {
        appStateObserver = NotificationCenter.default.addObserver(
            forName: UIApplication.willResignActiveNotification,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            self?.handleAppWillResignActive()
        }
    }

    // MARK: - 屏幕录制检测（增强版）
    private func setupScreenCaptureDetection() {
        if #available(iOS 11.0, *) {
            // 监听屏幕捕获状态变化
            screenCaptureObserver = NotificationCenter.default.addObserver(
                forName: UIScreen.capturedDidChangeNotification,
                object: nil,
                queue: .main
            ) { [weak self] notification in
                self?.handleScreenCaptureChange()
            }

            // 启动时检查当前状态
            if UIScreen.main.isCaptured {
                print("⚠️ 启动时检测到屏幕正在被捕获")
                handleScreenCaptureChange()
            }

            print("📹 屏幕捕获检测已启用 (iOS 11+)")
        } else {
            print("⚠️ 屏幕捕获检测需要 iOS 11+")
        }
    }

    // MARK: - 传统截屏通知
    private func setupScreenshotNotification() {
        screenshotObserver = NotificationCenter.default.addObserver(
            forName: UIApplication.userDidTakeScreenshotNotification,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            self?.handleScreenshotNotification()
        }
    }

    // MARK: - 停止所有检测
    private func stopAllDetection() {
        // 停止截屏通知监听
        if let observer = screenshotObserver {
            NotificationCenter.default.removeObserver(observer)
            screenshotObserver = nil
        }

        // 停止应用状态监听
        if let observer = appStateObserver {
            NotificationCenter.default.removeObserver(observer)
            appStateObserver = nil
        }

        // 停止屏幕录制监听
        if let observer = screenCaptureObserver {
            NotificationCenter.default.removeObserver(observer)
            screenCaptureObserver = nil
        }

        // 停止定时器
        screenCaptureDetectionTimer?.invalidate()
        screenCaptureDetectionTimer = nil

        print("🛑 所有检测系统已停止")
    }

    // MARK: - 处理应用失去焦点事件（最早期检测）
    private func handleAppWillResignActive() {
        guard isInterceptionEnabled else { return }

        isAppActive = false
        let now = Date()
        let timeSinceLastActive = now.timeIntervalSince(lastActiveTime)

        // 如果应用刚刚活跃，可能是截屏操作
        if timeSinceLastActive < 0.5 {
            print("⚡ 检测到应用快速失去焦点，可能是截屏操作")
            triggerProtection(reason: "应用状态变化")
        }

        lastActiveTime = now
    }

    // MARK: - 处理屏幕录制状态变化（智能版）
    @available(iOS 11.0, *)
    private func handleScreenCaptureChange() {
        guard isInterceptionEnabled else {
            updateScreenCaptureStatus()
            return
        }

        let isCaptured = UIScreen.main.isCaptured
        let wasBeingCaptured = isScreenBeingCaptured
        isScreenBeingCaptured = isCaptured

        // 更新状态显示
        updateScreenCaptureStatus()

        if isCaptured && !wasBeingCaptured {
            // 屏幕捕获开始
            print("🎯 屏幕捕获开始 - 这是最准确的截屏检测方法")

            DispatchQueue.main.async { [weak self] in
                self?.lastOperationStatus = "🎯 屏幕捕获检测 - 保护激活"

                // 立即显示保护层（如果不是预防性模式）
                if !(self?.isPreventiveMode ?? false) {
                    self?.showProtectionOverlay()
                }
            }

            // 对于截屏操作，通常捕获状态很短暂，设置较短的保护时间
            if !isPreventiveMode {
                DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) { [weak self] in
                    // 检查捕获状态是否仍然存在
                    if !(self?.isScreenBeingCaptured ?? false) {
                        self?.hideProtectionOverlay()
                        self?.lastOperationStatus = "✅ 屏幕捕获保护完成"
                    }
                }
            }

        } else if !isCaptured && wasBeingCaptured {
            // 屏幕捕获结束
            print("📱 屏幕捕获结束")

            DispatchQueue.main.async { [weak self] in
                // 如果不是预防性模式，延迟隐藏保护层
                if !(self?.isPreventiveMode ?? false) && (self?.isProtectionOverlayVisible ?? false) {
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) { [weak self] in
                        self?.hideProtectionOverlay()
                        self?.lastOperationStatus = "✅ 屏幕捕获结束，保护解除"
                    }
                }
            }
        }
    }

    // MARK: - 更新屏幕捕获状态显示
    @available(iOS 11.0, *)
    private func updateScreenCaptureStatus() {
        DispatchQueue.main.async { [weak self] in
            if UIScreen.main.isCaptured {
                self?.screenCaptureStatus = "🔴 正在捕获"
            } else {
                self?.screenCaptureStatus = "🟢 正常状态"
            }
        }
    }

    // MARK: - 处理传统截屏通知（后备检测）
    private func handleScreenshotNotification() {
        guard isInterceptionEnabled else { return }

        print("📸 收到截屏完成通知（后备检测）")
        // 这时截屏已经完成，但我们仍然显示保护层作为反馈
        triggerProtection(reason: "截屏通知")
    }

    // MARK: - 触发保护机制
    private func triggerProtection(reason: String) {
        print("🛡️ 触发保护机制，原因：\(reason)")

        DispatchQueue.main.async { [weak self] in
            self?.lastOperationStatus = "🔒 保护已激活 (\(reason))"

            // 如果不是预防性模式，立即显示保护层
            if !(self?.isPreventiveMode ?? false) {
                self?.showProtectionOverlay()
            }
        }

        // 如果不是预防性模式，需要延迟隐藏
        if !isPreventiveMode {
            DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) { [weak self] in
                self?.hideProtectionOverlay()
                self?.lastOperationStatus = "✅ 保护完成"
            }
        }
    }

    // MARK: - 显示保护覆盖层（优化版）
    private func showProtectionOverlay() {
        // 如果已经显示，不重复显示
        guard !isProtectionOverlayVisible else {
            print("ℹ️ 保护覆盖层已在显示中")
            return
        }

        print("🛡️ 快速显示保护覆盖层...")

        // 确保在主线程执行UI操作
        guard Thread.isMainThread else {
            DispatchQueue.main.async { [weak self] in
                self?.showProtectionOverlay()
            }
            return
        }

        // 获取当前窗口场景
        guard let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene else {
            print("❌ 无法获取窗口场景")
            return
        }

        // 如果窗口不存在，创建新窗口
        if protectionWindow == nil {
            protectionWindow = UIWindow(windowScene: windowScene)
            protectionWindow?.windowLevel = UIWindow.Level.alert + 1000 // 确保在最顶层
            protectionWindow?.backgroundColor = .clear

            // 创建保护视图控制器
            let protectionVC = ScreenshotProtectionViewController()
            protectionWindow?.rootViewController = protectionVC
        }

        // 快速显示窗口
        protectionWindow?.isHidden = false
        protectionWindow?.makeKeyAndVisible()
        isProtectionOverlayVisible = true

        print("⚡ 保护覆盖层已快速显示")
    }

    // MARK: - 隐藏保护覆盖层
    private func hideProtectionOverlay() {
        // 如果预防性模式开启，不隐藏覆盖层
        if isPreventiveMode {
            print("🔒 预防性模式开启，保持覆盖层显示")
            return
        }

        // 如果已经隐藏，不重复操作
        guard isProtectionOverlayVisible else {
            print("ℹ️ 保护覆盖层已隐藏")
            return
        }

        print("🔄 隐藏保护覆盖层...")

        // 确保在主线程执行UI操作
        guard Thread.isMainThread else {
            DispatchQueue.main.async { [weak self] in
                self?.hideProtectionOverlay()
            }
            return
        }

        // 隐藏窗口但保留实例以便快速重新显示
        protectionWindow?.isHidden = true
        isProtectionOverlayVisible = false

        print("✅ 保护覆盖层已隐藏")
    }
}

// MARK: - 截屏保护视图控制器
class ScreenshotProtectionViewController: UIViewController {

    override func viewDidLoad() {
        super.viewDidLoad()
        setupProtectionView()
    }

    private func setupProtectionView() {
        // 设置背景色
        view.backgroundColor = UIColor.systemBackground

        // 获取屏幕尺寸
        let screenBounds = UIScreen.main.bounds

        // 使用UIGraphicsImageRenderer生成保护内容
        let renderer = UIGraphicsImageRenderer(size: screenBounds.size)
        let protectionImage = renderer.image { context in
            // 绘制背景
            UIColor.systemBackground.setFill()
            context.fill(screenBounds)

            // 添加应用信息
            let appName = Bundle.main.infoDictionary?["CFBundleName"] as? String ?? "CDSS Demo"
            let titleAttributes: [NSAttributedString.Key: Any] = [
                .font: UIFont.systemFont(ofSize: 32, weight: .bold),
                .foregroundColor: UIColor.label
            ]

            let titleSize = appName.size(withAttributes: titleAttributes)
            let titleRect = CGRect(
                x: (screenBounds.width - titleSize.width) / 2,
                y: screenBounds.height / 3,
                width: titleSize.width,
                height: titleSize.height
            )
            appName.draw(in: titleRect, withAttributes: titleAttributes)

            // 添加保护提示
            let protectionText = "🛡️ 截屏保护已激活"
            let protectionAttributes: [NSAttributedString.Key: Any] = [
                .font: UIFont.systemFont(ofSize: 28, weight: .bold),
                .foregroundColor: UIColor.systemGreen
            ]

            let protectionSize = protectionText.size(withAttributes: protectionAttributes)
            let protectionRect = CGRect(
                x: (screenBounds.width - protectionSize.width) / 2,
                y: screenBounds.height / 2,
                width: protectionSize.width,
                height: protectionSize.height
            )
            protectionText.draw(in: protectionRect, withAttributes: protectionAttributes)

            // 添加说明文字
            let descriptionText = "多层检测系统已拦截截屏"
            let descriptionAttributes: [NSAttributedString.Key: Any] = [
                .font: UIFont.systemFont(ofSize: 18, weight: .medium),
                .foregroundColor: UIColor.secondaryLabel
            ]

            let descriptionSize = descriptionText.size(withAttributes: descriptionAttributes)
            let descriptionRect = CGRect(
                x: (screenBounds.width - descriptionSize.width) / 2,
                y: screenBounds.height / 2 + 50,
                width: descriptionSize.width,
                height: descriptionSize.height
            )
            descriptionText.draw(in: descriptionRect, withAttributes: descriptionAttributes)

            // 添加时间戳
            let formatter = DateFormatter()
            formatter.dateStyle = .medium
            formatter.timeStyle = .short
            let timestamp = formatter.string(from: Date())

            let timestampAttributes: [NSAttributedString.Key: Any] = [
                .font: UIFont.systemFont(ofSize: 14),
                .foregroundColor: UIColor.tertiaryLabel
            ]

            let timestampSize = timestamp.size(withAttributes: timestampAttributes)
            let timestampRect = CGRect(
                x: (screenBounds.width - timestampSize.width) / 2,
                y: screenBounds.height * 0.7,
                width: timestampSize.width,
                height: timestampSize.height
            )
            timestamp.draw(in: timestampRect, withAttributes: timestampAttributes)

            // 添加水印
            let watermarkText = "© 2024 CDSS Demo - 安全截屏"
            let watermarkAttributes: [NSAttributedString.Key: Any] = [
                .font: UIFont.systemFont(ofSize: 12, weight: .medium),
                .foregroundColor: UIColor.quaternaryLabel
            ]

            let watermarkSize = watermarkText.size(withAttributes: watermarkAttributes)
            let watermarkRect = CGRect(
                x: screenBounds.width - watermarkSize.width - 20,
                y: screenBounds.height - watermarkSize.height - 40,
                width: watermarkSize.width,
                height: watermarkSize.height
            )
            watermarkText.draw(in: watermarkRect, withAttributes: watermarkAttributes)
        }

        // 创建图像视图并设置为背景
        let imageView = UIImageView(image: protectionImage)
        imageView.frame = view.bounds
        imageView.contentMode = .scaleAspectFill
        view.addSubview(imageView)

        // 确保覆盖整个屏幕，包括安全区域
        imageView.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            imageView.topAnchor.constraint(equalTo: view.topAnchor),
            imageView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            imageView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            imageView.bottomAnchor.constraint(equalTo: view.bottomAnchor)
        ])
    }

    override var prefersStatusBarHidden: Bool {
        return false // 保持状态栏显示，但内容会被保护
    }

    override var preferredStatusBarStyle: UIStatusBarStyle {
        return .default
    }







}

@available(iOS 14.0, *)
extension WatermarkManager {
    // MARK: - 使用现代API的截屏方法
    func takeScreenshotWithModernAPI(watermarkText: String = "© 2024 CDSS Demo") {
        isProcessing = true

        // 使用iOS 14+的截屏API
        if #available(iOS 14.0, *) {
            DispatchQueue.main.async { [weak self] in
                self?.captureScreenWithModernAPI(watermarkText: watermarkText)
            }
        } else {
            // 降级到创建测试图片
            createTestImageWithWatermark(watermarkText: watermarkText)
        }
    }

    @available(iOS 14.0, *)
    private func captureScreenWithModernAPI(watermarkText: String) {
        guard let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene else {
            createTestImageWithWatermark(watermarkText: watermarkText)
            return
        }

        // 尝试获取屏幕内容
        let renderer = UIGraphicsImageRenderer(size: windowScene.screen.bounds.size)
        let screenshot = renderer.image { context in
            // 创建一个安全的演示内容
            let bounds = windowScene.screen.bounds

            // 绘制背景
            UIColor.systemBackground.setFill()
            context.fill(bounds)

            // 添加应用内容的模拟
            let appName = Bundle.main.infoDictionary?["CFBundleName"] as? String ?? "CDSS Demo"
            let titleAttributes: [NSAttributedString.Key: Any] = [
                .font: UIFont.systemFont(ofSize: 28, weight: .bold),
                .foregroundColor: UIColor.label
            ]

            let titleSize = appName.size(withAttributes: titleAttributes)
            let titleRect = CGRect(
                x: (bounds.width - titleSize.width) / 2,
                y: bounds.height / 3,
                width: titleSize.width,
                height: titleSize.height
            )
            appName.draw(in: titleRect, withAttributes: titleAttributes)

            // 添加时间戳
            let formatter = DateFormatter()
            formatter.dateStyle = .medium
            formatter.timeStyle = .short
            let timestamp = formatter.string(from: Date())

            let timestampAttributes: [NSAttributedString.Key: Any] = [
                .font: UIFont.systemFont(ofSize: 16),
                .foregroundColor: UIColor.secondaryLabel
            ]

            let timestampSize = timestamp.size(withAttributes: timestampAttributes)
            let timestampRect = CGRect(
                x: (bounds.width - timestampSize.width) / 2,
                y: bounds.height / 2,
                width: timestampSize.width,
                height: timestampSize.height
            )
            timestamp.draw(in: timestampRect, withAttributes: timestampAttributes)
        }

        processScreenshot(screenshot, watermarkText: watermarkText, logoName: nil)
    }
}

class WatermarkManager: ObservableObject {
    @Published var isProcessing = false
    @Published var showAlert = false
    @Published var alertMessage = ""
    
    // MARK: - 截屏并添加水印
    func takeScreenshotWithWatermark(watermarkText: String = "© 2024 CDSS Demo", logoName: String? = nil) {
        isProcessing = true

        // 使用更安全的截屏方法
        DispatchQueue.main.async { [weak self] in
            self?.captureScreenshot(watermarkText: watermarkText, logoName: logoName)
        }
    }

    // MARK: - 安全的截屏实现
    private func captureScreenshot(watermarkText: String, logoName: String?) {
        // 方法1：尝试使用窗口截屏
        if let screenshot = captureWindowScreenshot() {
            processScreenshot(screenshot, watermarkText: watermarkText, logoName: logoName)
            return
        }

        // 方法2：如果窗口截屏失败，创建一个演示图片
        let demoImage = createDemoImage()
        processScreenshot(demoImage, watermarkText: watermarkText, logoName: logoName)
    }

    // MARK: - 窗口截屏
    private func captureWindowScreenshot() -> UIImage? {
        guard let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
              let window = windowScene.windows.first else {
            return nil
        }

        // 使用更安全的截屏方法
        let renderer = UIGraphicsImageRenderer(size: window.bounds.size)

        return renderer.image { context in
            // 尝试安全的绘制方法
            if window.responds(to: #selector(UIView.drawHierarchy(in:afterScreenUpdates:))) {
                do {
                    window.drawHierarchy(in: window.bounds, afterScreenUpdates: false)
                } catch {
                    // 如果出错，绘制一个简单的背景
                    UIColor.systemBackground.setFill()
                    context.fill(window.bounds)

                    // 添加一些演示内容
                    let text = "截屏演示"
                    let attributes: [NSAttributedString.Key: Any] = [
                        .font: UIFont.systemFont(ofSize: 24, weight: .medium),
                        .foregroundColor: UIColor.label
                    ]
                    let textSize = text.size(withAttributes: attributes)
                    let textRect = CGRect(
                        x: (window.bounds.width - textSize.width) / 2,
                        y: (window.bounds.height - textSize.height) / 2,
                        width: textSize.width,
                        height: textSize.height
                    )
                    text.draw(in: textRect, withAttributes: attributes)
                }
            }
        }
    }

    // MARK: - 创建演示图片
    private func createDemoImage() -> UIImage {
        let size = CGSize(width: 375, height: 812) // iPhone标准尺寸
        let renderer = UIGraphicsImageRenderer(size: size)

        return renderer.image { context in
            // 绘制渐变背景
            let colors = [UIColor.systemBlue.cgColor, UIColor.systemPurple.cgColor]
            let gradient = CGGradient(colorsSpace: CGColorSpaceCreateDeviceRGB(), colors: colors as CFArray, locations: nil)!
            context.cgContext.drawLinearGradient(gradient,
                                               start: CGPoint(x: 0, y: 0),
                                               end: CGPoint(x: size.width, y: size.height),
                                               options: [])

            // 添加演示文字
            let title = "截屏演示"
            let subtitle = "这是一个演示截图"

            let titleAttributes: [NSAttributedString.Key: Any] = [
                .font: UIFont.systemFont(ofSize: 32, weight: .bold),
                .foregroundColor: UIColor.white
            ]

            let subtitleAttributes: [NSAttributedString.Key: Any] = [
                .font: UIFont.systemFont(ofSize: 18, weight: .medium),
                .foregroundColor: UIColor.white.withAlphaComponent(0.8)
            ]

            let titleSize = title.size(withAttributes: titleAttributes)
            let subtitleSize = subtitle.size(withAttributes: subtitleAttributes)

            let titleRect = CGRect(
                x: (size.width - titleSize.width) / 2,
                y: size.height / 2 - 30,
                width: titleSize.width,
                height: titleSize.height
            )

            let subtitleRect = CGRect(
                x: (size.width - subtitleSize.width) / 2,
                y: size.height / 2 + 10,
                width: subtitleSize.width,
                height: subtitleSize.height
            )

            title.draw(in: titleRect, withAttributes: titleAttributes)
            subtitle.draw(in: subtitleRect, withAttributes: subtitleAttributes)
        }
    }

    // MARK: - 处理截屏结果
    private func processScreenshot(_ screenshot: UIImage, watermarkText: String, logoName: String?) {
        // 添加水印
        let watermarkedImage = addWatermarkToImage(screenshot,
                                                 watermarkText: watermarkText,
                                                 logoName: logoName)

        // 保存到相册
        saveImageToPhotoLibrary(watermarkedImage)
    }

    // MARK: - 创建测试图片（用于模拟器测试）
    func createTestImageWithWatermark(watermarkText: String = "© 2024 CDSS Demo") {
        isProcessing = true

        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }

            // 创建一个测试图片
            let testImage = self.createDemoImage()

            // 添加水印
            let watermarkedImage = self.addWatermarkToImage(testImage,
                                                          watermarkText: watermarkText,
                                                          logoName: nil)

            // 保存到相册
            self.saveImageToPhotoLibrary(watermarkedImage)
        }
    }
    
    // MARK: - 给图片添加水印
    func addWatermarkToImage(_ originalImage: UIImage, 
                           watermarkText: String, 
                           logoName: String? = nil) -> UIImage {
        
        let renderer = UIGraphicsImageRenderer(size: originalImage.size)
        
        return renderer.image { context in
            // 绘制原始图片
            originalImage.draw(in: CGRect(origin: .zero, size: originalImage.size))
            
            // 添加Logo水印（如果提供）
            if let logoName = logoName, let logoImage = UIImage(named: logoName) {
                addLogoWatermark(logoImage, to: originalImage.size)
            }
            
            // 添加文字水印
            addTextWatermark(watermarkText, to: originalImage.size)
        }
    }
    
    // MARK: - 添加Logo水印
    private func addLogoWatermark(_ logoImage: UIImage, to imageSize: CGSize) {
        let logoSize = CGSize(
            width: imageSize.width * 0.15,  // Logo占图片宽度的15%
            height: imageSize.width * 0.15 * logoImage.size.height / logoImage.size.width
        )
        
        let logoRect = CGRect(
            x: imageSize.width - logoSize.width - 20,
            y: 20,
            width: logoSize.width,
            height: logoSize.height
        )
        
        logoImage.draw(in: logoRect)
    }
    
    // MARK: - 添加文字水印
    private func addTextWatermark(_ text: String, to imageSize: CGSize) {
        addTextWatermark(text,
                        to: imageSize,
                        position: .bottomRight,
                        style: .withBackground,
                        fontSize: imageSize.width * 0.04,
                        opacity: 0.8,
                        color: .white)
    }

    // MARK: - 添加自定义文字水印
    func addTextWatermark(_ text: String,
                         to imageSize: CGSize,
                         position: WatermarkPosition = .bottomRight,
                         style: WatermarkStyle = .withBackground,
                         fontSize: CGFloat = 16,
                         opacity: CGFloat = 0.8,
                         color: UIColor = .white) {

        // 文字属性
        let attributes: [NSAttributedString.Key: Any] = [
            .font: UIFont.systemFont(ofSize: fontSize, weight: .medium),
            .foregroundColor: color.withAlphaComponent(opacity),
            .strokeColor: UIColor.black.withAlphaComponent(0.8),
            .strokeWidth: style == .custom ? -1.0 : 0
        ]

        let textSize = text.size(withAttributes: attributes)
        let padding: CGFloat = 8

        // 计算位置
        let rect = calculateWatermarkRect(for: textSize,
                                        in: imageSize,
                                        position: position,
                                        padding: padding)

        // 根据样式绘制
        switch style {
        case .simple:
            text.draw(in: rect, withAttributes: attributes)

        case .withBackground, .withLogo:
            // 绘制背景
            let backgroundRect = CGRect(
                x: rect.origin.x - padding,
                y: rect.origin.y - padding,
                width: rect.width + padding * 2,
                height: rect.height + padding * 2
            )

            let backgroundColor = UIColor.black.withAlphaComponent(0.6)
            backgroundColor.setFill()
            let backgroundPath = UIBezierPath(roundedRect: backgroundRect, cornerRadius: 8)
            backgroundPath.fill()

            // 绘制文字
            if style == .withLogo {
                // 添加图标
                let iconSize: CGFloat = fontSize * 0.8
                let iconRect = CGRect(x: rect.origin.x - iconSize - 4,
                                    y: rect.origin.y + (rect.height - iconSize) / 2,
                                    width: iconSize,
                                    height: iconSize)

                if let starImage = UIImage(systemName: "star.fill") {
                    starImage.withTintColor(color.withAlphaComponent(opacity)).draw(in: iconRect)
                }
            }

            text.draw(in: rect, withAttributes: attributes)

        case .custom:
            // 自定义样式：带阴影的文字
            let shadowAttributes: [NSAttributedString.Key: Any] = [
                .font: UIFont.systemFont(ofSize: fontSize, weight: .bold),
                .foregroundColor: UIColor.black.withAlphaComponent(0.5),
                .strokeWidth: 0
            ]

            // 绘制阴影
            let shadowRect = CGRect(x: rect.origin.x + 1,
                                  y: rect.origin.y + 1,
                                  width: rect.width,
                                  height: rect.height)
            text.draw(in: shadowRect, withAttributes: shadowAttributes)

            // 绘制主文字
            let mainAttributes: [NSAttributedString.Key: Any] = [
                .font: UIFont.systemFont(ofSize: fontSize, weight: .bold),
                .foregroundColor: color.withAlphaComponent(opacity)
            ]
            text.draw(in: rect, withAttributes: mainAttributes)
        }
    }

    // MARK: - 计算水印位置
    private func calculateWatermarkRect(for textSize: CGSize,
                                      in imageSize: CGSize,
                                      position: WatermarkPosition,
                                      padding: CGFloat) -> CGRect {
        let margin: CGFloat = 20

        switch position {
        case .topLeft:
            return CGRect(x: margin, y: margin, width: textSize.width, height: textSize.height)
        case .topRight:
            return CGRect(x: imageSize.width - textSize.width - margin,
                         y: margin,
                         width: textSize.width,
                         height: textSize.height)
        case .bottomLeft:
            return CGRect(x: margin,
                         y: imageSize.height - textSize.height - margin,
                         width: textSize.width,
                         height: textSize.height)
        case .bottomRight:
            return CGRect(x: imageSize.width - textSize.width - margin,
                         y: imageSize.height - textSize.height - margin,
                         width: textSize.width,
                         height: textSize.height)
        case .center:
            return CGRect(x: (imageSize.width - textSize.width) / 2,
                         y: (imageSize.height - textSize.height) / 2,
                         width: textSize.width,
                         height: textSize.height)
        }
    }
    
    // MARK: - 保存图片到相册
    func saveImageToPhotoLibrary(_ image: UIImage) {
        PHPhotoLibrary.requestAuthorization { [weak self] status in
            DispatchQueue.main.async {
                switch status {
                case .authorized, .limited:
                    self?.saveImageWithModernAPI(image)
                case .denied, .restricted:
                    self?.showError("需要相册访问权限才能保存截图")
                case .notDetermined:
                    self?.showError("相册权限未确定")
                @unknown default:
                    self?.showError("未知的权限状态")
                }
            }
        }
    }

    // MARK: - 使用现代API保存图片
    private func saveImageWithModernAPI(_ image: UIImage) {
        PHPhotoLibrary.shared().performChanges({
            PHAssetChangeRequest.creationRequestForAsset(from: image)
        }) { [weak self] success, error in
            DispatchQueue.main.async {
                self?.isProcessing = false

                if success {
                    self?.showSuccess("图片已保存到相册")
                } else if let error = error {
                    self?.showError("保存失败: \(error.localizedDescription)")
                } else {
                    self?.showError("保存失败：未知错误")
                }
            }
        }
    }
    
    // MARK: - 显示错误信息
    private func showError(_ message: String) {
        alertMessage = message
        showAlert = true
    }
    
    // MARK: - 显示成功信息
    private func showSuccess(_ message: String) {
        alertMessage = message
        showAlert = true
    }
}

// MARK: - 水印样式枚举
enum WatermarkStyle {
    case simple          // 简单文字水印
    case withBackground  // 带背景的文字水印
    case withLogo       // 带Logo的水印
    case custom         // 自定义样式
}

// MARK: - 水印位置枚举
enum WatermarkPosition {
    case topLeft
    case topRight
    case bottomLeft
    case bottomRight
    case center
}
